import apiClient from "./api";

export interface ResultDTO<T = any> {
  code: number;
  message: string;
  data: T;
}

export async function getGoldPrice(
  dto: PriceManualDTO = {}
): Promise<ResultDTO<PriceManual[]>> {
  const resp = await apiClient.post<ResultDTO<PriceManual[]>>("/priceManual/getGoldPrice", dto);
  return resp.data; // 关键：解开 AxiosResponse
}

export async function addGoldPrice(dto: PriceManualDTO): Promise<ResultDTO<string>> {
  const resp = await apiClient.post<ResultDTO<string>>("/priceManual/addGoldPrice", dto);
  return resp.data;
}

export async function updateGoldPrice(dto: PriceManualDTO): Promise<ResultDTO<number>> {
  const resp = await apiClient.put<ResultDTO<number>>("/priceManual/updateGoldPrice", dto);
  return resp.data;
}

export async function deleteGoldPrice(dto: Pick<PriceManualDTO, "id">): Promise<ResultDTO<number>> {
  const resp = await apiClient.delete<ResultDTO<number>>("/priceManual/deleteGoldPrice", { data: dto });
  return resp.data;
}