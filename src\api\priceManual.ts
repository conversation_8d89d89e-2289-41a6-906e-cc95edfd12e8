import apiClient from "./api";

export interface ResultDTO<T = any> {
  code: number;
  message: string;
  data: T;
}

// 金价数据类型定义
export interface PriceManual {
  id?: number;
  purityCode: string;        // 成色/品类编码
  itemCategory: string;      // 类别
  pricePerG: number;         // 参考价(元/克)
  effectiveFrom?: string;    // 生效时间
  status: number;            // 状态 (1: 启用, 0: 停用)
  remark?: string;           // 备注
}

// 请求参数类型定义
export interface PriceManualDTO {
  id?: number;
  purityCode?: string;
  itemCategory?: string;
  pricePerG?: number;
  effectiveFrom?: string;
  status?: number;
  remark?: string;
}

export async function getGoldPrice(
  dto: PriceManualDTO = {}
): Promise<ResultDTO<PriceManual[]>> {
  const resp = await apiClient.post<ResultDTO<PriceManual[]>>("/priceManual/getGoldPrice", dto);
  return resp.data; // 关键：解开 AxiosResponse
}

export async function addGoldPrice(dto: PriceManualDTO): Promise<ResultDTO<string>> {
  const resp = await apiClient.post<ResultDTO<string>>("/priceManual/addGoldPrice", dto);
  return resp.data;
}

export async function updateGoldPrice(dto: PriceManualDTO): Promise<ResultDTO<number>> {
  const resp = await apiClient.put<ResultDTO<number>>("/priceManual/updateGoldPrice", dto);
  return resp.data;
}

export async function deleteGoldPrice(dto: Pick<PriceManualDTO, "id">): Promise<ResultDTO<number>> {
  const resp = await apiClient.delete<ResultDTO<number>>("/priceManual/deleteGoldPrice", { data: dto });
  return resp.data;
}